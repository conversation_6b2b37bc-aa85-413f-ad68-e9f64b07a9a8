import { useState, useEffect, useRef, FC, ReactNode } from "react";
import { useQuery } from "@tanstack/react-query";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  ArrowDownRight,
  ArrowRight,
  ArrowUpRight,
  BarChart,
  Calendar,
  DollarSign,
  Heart,
  MoreVertical,
  MousePointerClick,
  Search,
  Settings,
  TrendingUp,
  Users,
  Zap,
} from "lucide-react";

// --- DATA SIMULATION (APP & BUSINESS FOCUSED) ---
const fetchBusinessAnalytics = async () => {
  await new Promise(res => setTimeout(res, 800));
  const mrr = 42850;
  const activeUsers = 12450;
  const netProfit = 15230;
  const churnRate = 1.8;
  
  return {
    kpis: {
      mrr: { value: mrr, change: 12.5 },
      activeUsers: { value: activeUsers, change: 8.2 },
      netProfit: { value: netProfit, change: 24.1 },
      churnRate: { value: churnRate, change: -15.3 },
    },
    financialChart: Array.from({ length: 30 }, (_, i) => ({
      day: i + 1,
      revenue: 35000 + Math.sin(i / 3) * 5000 + Math.random() * 2000 + i * 200,
      profit: 12000 + Math.sin(i / 3) * 3000 + Math.random() * 1000 + i * 100,
      events: i === 10 ? "Marketing Campaign" : i === 22 ? "New Feature Launch" : null,
    })),
    userFunnel: [
      { stage: "Website Visitors", value: 82456, change: 12.1 },
      { stage: "Sign-ups", value: 18965, change: 8.9 },
      { stage: "Active Trials", value: 9234, change: 6.2 },
      { stage: "Paid Subscribers", value: 3456, change: 4.1 },
    ],
    unitEconomics: {
      ltv: { value: 245.8, change: 5.2 },
      cac: { value: 82.3, change: -3.1 },
      ratio: { value: 3.0, change: 8.3 },
    },
    appHealth: {
      uptime: { value: 99.98, status: "success" },
      avgLatency: { value: 112, unit: "ms", status: "success" },
      crashFreeRate: { value: 99.7, status: "success" },
    },
  };
};

// --- UI COMPONENTS (Modern & Glassy) ---

const GlassCard: FC<{ children: ReactNode; className?: string }> = ({ children, className }) => (
  <div className={`bg-white/60 backdrop-blur-2xl border border-white/30 rounded-2xl shadow-lg shadow-blue-500/5 transition-all duration-300 hover:shadow-xl hover:shadow-blue-500/10 ${className}`}>
    {children}
  </div>
);

const KPICard: FC<{ title: string; value: string; change: number; icon: ReactNode }> = ({ title, value, change, icon }) => (
  <GlassCard>
    <div className="p-5">
      <div className="flex items-center justify-between text-gray-500">
        <span className="text-sm font-medium">{title}</span>
        {icon}
      </div>
      <div className="mt-2 flex items-baseline space-x-2">
        <span className="text-3xl font-bold text-gray-800">{value}</span>
        <span className={`flex items-center text-sm font-medium ${change > 0 ? "text-emerald-500" : "text-red-500"}`}>
          {change > 0 ? <TrendingUp className="w-4 h-4" /> : <ArrowDownRight className="w-4 h-4" />}
          <span className="ml-1">{Math.abs(change)}%</span>
        </span>
      </div>
    </div>
  </GlassCard>
);

const FinancialPerformanceChart: FC<{ data: any[] }> = ({ data }) => {
  const svgRef = useRef<SVGSVGElement>(null);
  const [tooltip, setTooltip] = useState<{ x: number; y: number; revenue: number; profit: number; day: number } | null>(null);

  const maxRevenue = Math.max(...data.map(d => d.revenue));
  
  const createPath = (key: 'revenue' | 'profit') => {
    return data.map((d, i) => {
      const x = (i / (data.length - 1)) * 100;
      const y = 100 - (d[key] / maxRevenue) * 100;
      return `${i === 0 ? "M" : "L"} ${x.toFixed(2)} ${y.toFixed(2)}`;
    }).join(" ");
  };

  const handleMouseMove = (e: React.MouseEvent) => {
    if (!svgRef.current) return;
    const rect = svgRef.current.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const i = Math.round((x / rect.width) * (data.length - 1));
    const point = data[i];
    if (point) {
      const y = rect.height - (point.revenue / maxRevenue) * rect.height;
      setTooltip({ x, y, revenue: point.revenue, profit: point.profit, day: point.day });
    }
  };
  
  return (
    <GlassCard>
      <div className="p-5">
        <div className="flex justify-between items-center mb-4">
            <div>
              <h3 className="text-base font-semibold text-gray-700">Financial Performance</h3>
              <p className="text-xs text-gray-500">Revenue vs. Profit</p>
            </div>
            <div className="flex items-center space-x-4 text-xs">
                <span className="flex items-center"><div className="w-2 h-2 rounded-full bg-blue-500 mr-2"/>Revenue</span>
                <span className="flex items-center"><div className="w-2 h-2 rounded-full bg-emerald-400 mr-2"/>Profit</span>
            </div>
        </div>
        <div className="h-72 relative" onMouseMove={handleMouseMove} onMouseLeave={() => setTooltip(null)}>
          <svg ref={svgRef} className="w-full h-full" viewBox="0 0 100 100" preserveAspectRatio="none">
            <defs>
              <linearGradient id="revenue-gradient" x1="0" y1="0" x2="0" y2="1">
                <stop offset="0%" stopColor="#3b82f6" stopOpacity="0.1" />
                <stop offset="100%" stopColor="#3b82f6" stopOpacity="0" />
              </linearGradient>
            </defs>
            <path d={createPath('revenue') + " V 100 H 0 Z"} fill="url(#revenue-gradient)" />
            <path d={createPath('revenue')} fill="none" stroke="#3b82f6" strokeWidth="2" className="chart-path" />
            <path d={createPath('profit')} fill="none" stroke="#34d399" strokeWidth="2" strokeDasharray="4 2" className="chart-path" style={{animationDelay: '0.2s'}}/>
            {data.map((d, i) => d.events && (
              <g key={i} transform={`translate(${(i / (data.length - 1)) * 100}, ${100 - (d.revenue / maxRevenue) * 100})`}>
                <circle r="4" fill="#3b82f6" stroke="white" strokeWidth="2"/>
                <text y="-8" textAnchor="middle" className="text-[8px] fill-gray-600 font-semibold">{d.events}</text>
              </g>
            ))}
            {tooltip && (
              <g transform={`translate(${ (tooltip.x / svgRef.current!.clientWidth) * 100 }, 0)`}>
                <line y1="0" y2="100" stroke="#9ca3af" strokeWidth="0.5" strokeDasharray="3 3"/>
                <foreignObject x={-50} y={tooltip.y-50} width="100" height="40">
                  <div className="bg-gray-800 text-white text-xs rounded-lg shadow-xl p-2 text-center">
                    <div>Rev: ${tooltip.revenue.toFixed(0)}</div>
                    <div>Profit: ${tooltip.profit.toFixed(0)}</div>
                  </div>
                </foreignObject>
              </g>
            )}
          </svg>
        </div>
      </div>
    </GlassCard>
  );
};

const UserGrowthFunnel: FC<{ data: any[] }> = ({ data }) => {
  const maxValue = data[0].value;
  return (
    <GlassCard>
      <div className="p-5">
        <h3 className="text-base font-semibold text-gray-700 mb-4">User Growth Funnel</h3>
        <div className="space-y-2">
          {data.map((item, index) => (
            <div key={item.stage} className="flex items-center space-x-4 group">
              <div className="w-8 h-8 flex-shrink-0 flex items-center justify-center bg-blue-100/50 rounded-full text-blue-600 font-semibold text-sm">
                {index + 1}
              </div>
              <div className="flex-grow">
                <div className="flex justify-between items-baseline">
                  <span className="text-sm text-gray-600 font-medium">{item.stage}</span>
                  <span className="text-lg font-bold text-gray-800">{item.value.toLocaleString()}</span>
                </div>
                <div className="h-3 bg-gray-200/80 rounded-full mt-1 overflow-hidden">
                   <div className="h-full bg-gradient-to-r from-sky-400 to-blue-500 rounded-full transition-all duration-500" style={{ width: `${(item.value / maxValue) * 100}%` }}/>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </GlassCard>
  );
};

// --- MAIN DASHBOARD COMPONENT ---
const Dashboard = () => {
  const { data, isLoading, error } = useQuery({
    queryKey: ["businessAnalytics"],
    queryFn: fetchBusinessAnalytics
  });

  if (isLoading) return <div className="p-8 text-center">Loading dashboard...</div>;
  if (error) return <div className="p-8 text-center text-red-500">Error loading dashboard.</div>;
  if (!data) return null;

  const { kpis, financialChart, userFunnel, unitEconomics, appHealth } = data;

  return (
    <div className="min-h-screen w-full bg-gradient-to-br from-gray-50 to-sky-100/30 p-4 sm:p-6 lg:p-8">
      <div className="max-w-screen-2xl mx-auto">
        <header className="flex flex-wrap items-center justify-between gap-4 mb-8">
          <div>
            <h1 className="text-2xl font-bold text-gray-800">Dashboard</h1>
            <p className="text-sm text-gray-500 mt-1 flex items-center">
              Welcome back! Here's what's happening with your business today.
              <span className="ml-2 w-2 h-2 bg-green-400 rounded-full animate-pulse" />
            </p>
          </div>
          <div className="flex items-center space-x-2">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 -translate-y-1/2 w-4 h-4 text-gray-400" />
              <Input placeholder="Search..." className="pl-9 w-40 sm:w-48 bg-white/60 backdrop-blur-sm border-white/50 rounded-full h-9 text-sm" />
            </div>
            <Button variant="ghost" size="icon" className="w-9 h-9 rounded-full bg-white/60 backdrop-blur-sm border-white/50"><Settings className="w-4 h-4 text-gray-500"/></Button>
          </div>
        </header>

        <main className="space-y-6">
          <section className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
            <KPICard title="MRR" value={`$${(kpis.mrr.value / 1000).toFixed(1)}k`} change={kpis.mrr.change} icon={<DollarSign className="w-5 h-5"/>} />
            <KPICard title="Active Users" value={(kpis.activeUsers.value / 1000).toFixed(1) + 'k'} change={kpis.activeUsers.change} icon={<Users className="w-5 h-5"/>} />
            <KPICard title="Net Profit" value={`$${(kpis.netProfit.value / 1000).toFixed(1)}k`} change={kpis.netProfit.change} icon={<Heart className="w-5 h-5"/>} />
            <KPICard title="Churn Rate" value={`${kpis.churnRate.value}%`} change={kpis.churnRate.change} icon={<TrendingUp className="w-5 h-5"/>} />
          </section>

          <section className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <div className="lg:col-span-2">
              <FinancialPerformanceChart data={financialChart} />
            </div>
            <div className="space-y-6">
              <UserGrowthFunnel data={userFunnel} />
              <GlassCard>
                <div className="p-5">
                   <h3 className="text-base font-semibold text-gray-700 mb-4">Unit Economics</h3>
                   <div className="space-y-3 text-sm">
                     <div className="flex justify-between"><span>Customer Lifetime Value (LTV)</span><span className="font-bold text-gray-800">${unitEconomics.ltv.value}</span></div>
                     <div className="flex justify-between"><span>Customer Acquisition Cost (CAC)</span><span className="font-bold text-gray-800">${unitEconomics.cac.value}</span></div>
                     <div className="flex justify-between text-base"><strong>LTV:CAC Ratio</strong><strong className="text-emerald-500">{unitEconomics.ratio.value}:1</strong></div>
                   </div>
                </div>
              </GlassCard>
               <GlassCard>
                <div className="p-5">
                   <h3 className="text-base font-semibold text-gray-700 mb-4 flex items-center"><Zap className="w-4 h-4 mr-2 text-sky-500"/>App Health</h3>
                   <div className="space-y-3 text-sm">
                     <div className="flex justify-between"><span>API Uptime</span><span className="font-semibold text-emerald-500">{appHealth.uptime.value}%</span></div>
                     <div className="flex justify-between"><span>Avg. API Latency</span><span className="font-semibold text-emerald-500">{appHealth.avgLatency.value}{appHealth.avgLatency.unit}</span></div>
                     <div className="flex justify-between"><span>Crash-Free Rate</span><span className="font-semibold text-emerald-500">{appHealth.crashFreeRate.value}%</span></div>
                   </div>
                </div>
              </GlassCard>
            </div>
          </section>
        </main>
      </div>
    </div>
  );
};

export default Dashboard;