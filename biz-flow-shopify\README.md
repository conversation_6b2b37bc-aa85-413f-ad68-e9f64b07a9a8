# Shopify Business Flow Application

A full-stack web application built with React, Express, and TypeScript for managing Shopify business workflows.

## 🚀 Quick Start

### Prerequisites
- Node.js (v18 or higher)
- npm or yarn

### Installation & Setup
```bash
# Install dependencies
npm install

# Setup database (if needed)
npm run db:push

# Start development server
npm run dev
```

The application will be available at `http://localhost:5000`

## 📜 Available Scripts

### Development
- **`npm run dev`** - Start the development server (cross-platform)
- **`npm run dev:windows`** - Start development server (Windows specific)
- **`npm run dev:unix`** - Start development server (Unix/Linux/Mac specific)
- **`npm run dev:clean`** - Clean build artifacts and start fresh development server

### Production
- **`npm run build`** - Build the application for production
- **`npm run start`** - Start the production server

### Utilities
- **`npm run check`** - Run TypeScript type checking
- **`npm run clean`** - Remove build artifacts and cache
- **`npm run setup`** - Install dependencies and setup database
- **`npm run install:clean`** - Clean install of dependencies

### Database
- **`npm run db:push`** - Push database schema changes

## 🏗️ Project Structure

```
biz-flow-shopify/
├── client/          # React frontend application
├── server/          # Express backend API
├── shared/          # Shared types and schemas
├── dist/           # Built application (generated)
└── package.json    # Project configuration
```

## 🛠️ Development

The application uses:
- **Frontend**: React + TypeScript + Vite
- **Backend**: Express + TypeScript
- **Database**: PostgreSQL with Drizzle ORM
- **Styling**: Tailwind CSS + Radix UI components

### Environment Variables
The development server automatically sets `NODE_ENV=development`. For production deployment, ensure `NODE_ENV=production` is set.

## 🔧 Troubleshooting

### Server Won't Start
1. Make sure all dependencies are installed: `npm install`
2. Check if port 5000 is available
3. Try cleaning and restarting: `npm run dev:clean`

### Build Issues
1. Run type checking: `npm run check`
2. Clean build artifacts: `npm run clean`
3. Reinstall dependencies: `npm run install:clean`

## 📝 Notes

- The development server serves both frontend and backend on port 5000
- Hot reloading is enabled for both client and server code
- The application uses cross-platform scripts for Windows/Unix compatibility
